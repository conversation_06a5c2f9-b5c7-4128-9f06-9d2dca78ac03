File System
Frontend Repository (astro-venue-frontend)
/src
|-- /assets
|   |-- /emails             // MJML templates for reminders/invites
|   |-- /icons
|   `-- /images
|-- /components
|   |-- /auth
|   |   |-- LoginForm.jsx
|   |   `-- RegisterForm.jsx
|   |-- /common
|   |   |-- Button.jsx
|   |   |-- Modal.jsx
|   |   |-- Spinner.jsx
|   |   `-- StarRating.jsx     // Interactive star rating component
|   |-- /core
|   |   |-- Header.astro
|   |   |-- Footer.astro
|   |   `-- Layout.astro
|   |-- /dashboard
|   |   |-- BookingList.jsx
|   |   |-- ChecklistForm.jsx  // For owners to create checklists
|   |   |-- ChecklistSubmission.jsx // For renters to complete them
|   |   |-- InvitationForm.jsx // For renters to send event invites
|   |   |-- OwnerDashboard.jsx
|   |   |-- ProfileForm.jsx
|   |   `-- ReviewForm.jsx       // For renters to leave a review
|   |-- /admin
|   |   |-- AdminDashboard.jsx
|   |   |-- FlaggedContentTable.jsx
|   |   `-- UserManagementTable.jsx
|   |-- /venues
|   |   |-- BookingForm.jsx
|   |   |-- PannellumViewer.jsx  // Native 360 viewer
|   |   |-- ReviewList.jsx       // Displays reviews on a venue page
|   |   |-- VenueCard.jsx
|   |   |-- VenueForm.jsx
|   |   `-- VenueSearch.jsx
|   `-- /transactions
|       |-- MessagingWindow.jsx
|       `-- PaystackPayment.jsx
|-- /layouts
|   `-- BaseLayout.astro
|-- /lib
|   |-- pocketbase.js
|   |-- meilisearch.js
|   `-- state.js
|-- /pages
|   |-- /auth
|   |   |-- login.astro
|   |   `-- register.astro
|   |-- /dashboard
|   |   |-- index.astro
|   |   `-- profile.astro
|   |-- /admin
|   |   `-- index.astro
|   |-- /venues
|   |   |-- index.astro
|   |   |-- new.astro
|   |   `-- [id].astro
|   |-- /bookings
|   |   |-- index.astro
|   |   `-- [id].astro       // Booking detail, chat, checklist, invite
|   |-- index.astro
|   `-- 404.astro
`-- /styles
    `-- global.css

Backend Repository (deno-venue-services)
/
|-- /src
|   |-- /services
|   |   |-- emailService.ts          // Wrapper for Resend/Postmark
|   |   |-- meilisearchSyncService.ts
|   |   |-- notificationService.ts   // Logic for sending alerts/reminders
|   |   |-- paystackWebhookService.ts
|   |   `-- payoutService.ts
|   |-- /types
|   |   |-- paystack.ts
|   |   `-- pocketbase.ts
|   `-- /utils
|       `-- logger.ts
|-- main.ts
|-- Dockerfile
|-- deps.ts
`-- .env.example

Feature Specifications
Feature 1: User, Admin & Profile Core
Feature Goal: To establish a secure and robust identity system that handles user registration, authentication, role-based access control (RBAC), and content moderation primitives. This is the bedrock of platform trust and safety.

API Relationships: This feature exclusively uses the Pocketbase API for all its operations. The frontend client will interact directly with the Pocketbase instance.

Detailed Feature Requirements:

Users must be able to register with an email and password an oAuth.

Passwords must meet strength requirements (e.g., min 8 chars, 1 uppercase, 1 number).

Upon registration, users are assigned the 'renter' role by default. They can add the 'owner' role from their profile.

Users must log in to access any authenticated parts of the application.

A user with the 'admin' role has access to a separate dashboard.

The admin dashboard must display a list of all users and a list of all flagged content.

Admins must be able to view user details and manually change a user's role or deactivate their account.

Admins must be able to view flagged content details and resolve flags (marking them as 'resolved').

Detailed Implementation Guide:
* **Detailed Implementation Guide:**

    1.  **System Architecture:** A standard client-server model where the Astro/React frontend acts as the client and Pocketbase is the BaaS. The admin dashboard is a set of pages within the Astro application located at `/admin/*`, protected by a layout that verifies the user's role from the global state store.
    2.  **Database Schema Design:**
          * **`users` Collection:**
              * `id`: (PK, default)
              * `email`: (Text, Unique, Required)
              * `name`: (Text, Required)
              * `avatar`: (File)
              * `roles`: (JSON, Required, Default: `["renter"]`). Can contain 'renter', 'owner', 'admin'.
              * `is_active`: (Bool, Default: `true`)
              * `paystack_customer_id`: (Text, optional)
              * `paystack_recipient_code`: (Text, optional, for Owners)
          * **`flagged_content` Collection:**
              * `id`: (PK, default)
              * `content_type`: (Select, Options: 'venue', 'user\_profile', Required)
              * `content_id`: (Text, Required)
              * `reporter`: (Relation to `users`, Required)
              * `reason`: (Text, Required)
              * `status`: (Select, Options: 'open', 'resolved', Default: 'open', Required)
    3.  **Comprehensive API Design (Pocketbase):**
          * `POST /api/collections/users/records`: Create a user. Publicly accessible.
          * `POST /api/collections/users/auth-with-password`: Authenticates a user, returns a JWT.
          * `GET /api/collections/users/records`: List users. API Rule: `@request.auth.roles ~ 'admin'`.
          * `PATCH /api/collections/users/records/:id`: Update a user. API Rule: `@request.auth.id = id || @request.auth.roles ~ 'admin'`.
          * `POST /api/collections/flagged_content/records`: Create a flag. API Rule: `@request.auth.id != ""`.
          * `GET /api/collections/flagged_content/records`: List flags. API Rule: `@request.auth.roles ~ 'admin'`.
    4.  **Frontend Architecture:**
          * State for the authenticated user (including roles) will be managed globally using Nano Stores. On app load, a request is made to refresh the auth state from the Pocketbase cookie.
          * A `AdminLayout.astro` will wrap all pages in `/src/pages/admin/`. This layout will check the global user store; if the user is not present or does not have the 'admin' role, it will redirect to the login page or a 403 page.
    5.  **Detailed CRUD Operations:**
          * **Create:** User registration validates email format and password strength on the client before sending to Pocketbase.
          * **Read:** A user can only read their own full record. An admin can read all records with pagination.
          * **Update:** A user can update their `name` and `avatar`. An admin can update any user's `roles` and `is_active` status.
          * **Delete:** No hard deletes. Admins perform a soft delete by setting `is_active = false`. This preserves user data for historical context (e.g., past bookings).
    6.  **Security Considerations:** JWTs returned by Pocketbase will be stored in a secure, `HttpOnly` cookie. All state-changing forms will implicitly use Pocketbase's anti-CSRF protection. All user-provided text (names, etc.) will be escaped on render to prevent XSS.
    7.  **Testing Strategy:** Unit tests for React form components. Integration tests to verify API rules in Pocketbase (e.g., a non-admin user attempting to list all users should fail). E2E test for the admin login flow and resolving a flagged item.
    8.  **Data Management:** User session is managed via the JWT. Admin table data (users, flags) will use server-side pagination handled by the Pocketbase API.
    9.  **Error Handling & Logging:** Client-side form errors will be displayed next to the relevant fields. API errors (e.g., 401, 403) will be caught by a global fetch wrapper, which will redirect to the login page if necessary. Pocketbase will log all failed API rule validations.

Feature 2: Venue Marketplace (Listing, Touring & Search)
Feature Goal: To provide a fluid and intuitive interface for owners to merchandise their venues effectively, including immersive virtual tours, and for renters to discover them through a powerful, fast search experience.

API Relationships: The frontend interacts with Pocketbase for all CRUD operations on venues and with Meilisearch for all search queries.

Detailed Feature Requirements:

An authenticated owner must be able to access a multi-step "Create Venue" form.

The form must capture title, description, address, capacity, pricing, amenities (as selectable tags), standard photos, one equirectangular 360° photo, and an optional PDF rental agreement.

(REQ010) The listing page must feature a native, interactive 360° photo viewer.

The search interface must provide real-time, typo-tolerant results as the user types.

Any authenticated user must be able to click a "Flag" button on a venue page, which opens a modal to submit a reason for flagging.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the virtual tour viewer and search functionality.)

Feature 3: Transaction Lifecycle (Booking, Messaging, Payments & Payouts)
Feature Goal: To manage the entire commercial flow in a secure, reliable, and auditable manner, from initial renter inquiry and communication, through booking and payment, to final owner payout.

API Relationships: Frontend <-> Pocketbase (Booking & Chat), Frontend -> Paystack (Payment), Deno Service <-> Paystack (Payouts & Webhooks).

Detailed Feature Requirements:

On a venue page, an authenticated renter can select a start and end date/time and request to book.

This request must trigger a check against existing confirmed bookings for that venue to prevent double-booking.

The property owner receives a notification and can approve or deny the request from their dashboard.

Upon approval, the renter receives a notification and a prompt to pay.

The renter pays the full amount via Paystack.

Upon successful payment, the booking status is updated, and both parties are notified.

After the booking's end date has passed, a payout is automatically scheduled for the owner.

(REQ008) A simple, real-time chat must be available within the context of a single booking, accessible only to the renter and owner.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the real-time messaging window and transaction flow.)

Feature 4: Community Trust & Feedback (Reviews & Ratings)
Feature Goal: (REQ009) To build a trustworthy marketplace by enabling renters to share their experiences and provide a transparent feedback loop for venue quality and owner conduct.

API Relationships: Frontend <-> Pocketbase. The Deno service may be used to calculate and cache average ratings periodically.

Detailed Feature Requirements:

After a booking's status becomes completed, the renter receives a prompt (via email and in-app notification) to leave a review.

The review form must capture a star rating (1-5) for "Overall Experience" and a free-text comment.

Submitted reviews must be associated with the specific booking, user, and venue.

Property owners must be notified of new reviews and have the ability to write a single, public response.

The average star rating and a list of all reviews (with owner responses nested) must be displayed on the public venue listing page.

Detailed Implementation Guide:

System Architecture: This is a client-to-BaaS feature. The frontend handles form submission, and the backend (Pocketbase) stores the data. A cron job in the Deno service can be added to periodically update a cached average_rating field on the venues collection to optimize frontend load times.

Database Schema Design:

reviews Collection:

id: (PK, default)

booking: (Relation to bookings, Required, Unique - one review per booking)

renter: (Relation to users, Required)

venue: (Relation to venues, Required)

rating: (Number, Required, Min: 1, Max: 5)

comment: (Text, Required)

owner_response: (Text, optional)

Comprehensive API Design (Pocketbase):

POST /api/collections/reviews/records: Create a review. API Rule: @request.auth.id = @request.data.renter && @request.data.booking.status = "completed".

GET /api/collections/reviews/records?filter=(venue.id='VENUE_ID'): List reviews for a venue. Publicly accessible.

PATCH /api/collections/reviews/records/:id: Update a review (for owner response). API Rule: @request.auth.id = owner.id && owner_response = "" (Can only respond once).

Frontend Architecture:

ReviewForm.jsx: A modal component that includes the StarRating.jsx component and a textarea.

ReviewList.jsx: A component on the venue page that fetches and displays all reviews, nesting the owner_response where it exists.

Detailed CRUD Operations:

Create: Renter submits the form. The API rule ensures they can only review a booking they've completed.

Read: Reviews are publicly readable on the venue page, sorted by most recent.

Update: An owner can add an owner_response one time. No other updates are allowed.

Delete: Deletions are admin-only operations for moderation purposes.

Security Considerations: The API rules are critical to prevent review spam or fraudulent reviews. All comments must be sanitized on the frontend before display to prevent XSS.

Testing Strategy: Integration test for the API rule that blocks reviews for incomplete bookings. E2E test for leaving a review and then seeing it appear on the venue page.

Data Management: Average ratings should be calculated and cached on the venues collection to avoid N+1 queries on the search page. Review lists will be paginated.

Error Handling & Logging: If a user tries to review a booking they are not authorized for, the API will return a 403, and the frontend will show a generic error message.

Feature 5: Lifecycle Communications & Logistics (Revised)
Feature Goal: (REQ013, REQ014, REQ015) To create a comprehensive and automated communication system that guides users through their entire journey—from onboarding and identity verification to booking logistics and post-rental engagement—improving user experience, trust, and operational efficiency.

API Relationships: Frontend <-> Pocketbase (for data & verification triggers). Deno Service <-> Email Provider (e.g., Resend). Pocketbase -> Deno Service (via webhook/subscription for event triggers).

Detailed Feature Requirements:

Welcome & Verification: New users automatically receive a welcome email. A separate verification email with a unique link is sent to confirm their identity. The application must restrict certain actions until the user's email is verified.

Booking Alerts: Key events (new booking request, confirmation, payment success, denial) must trigger automated emails to the relevant parties.

Check-in/Checkout Alerts: Automated emails are sent shortly before the booking's start_date (e.g., 24 hours and 1 hour before) and end_date.

Checklists: Owners can define a simple move-out checklist template for their venues. Before checkout, renters can access and complete this checklist.

Invitations: After a booking is paid, renters have an option to create a simple event invitation, input guest emails, and have the system send them out.

Detailed Implementation Guide:

System Architecture: A hybrid model. Pocketbase will natively handle the critical identity-related emails (email verification, password reset) as they are tied directly to its auth system. The Deno service will handle all other transactional and marketing emails (Welcome, Reminders, Check-in/out, etc.) to allow for rich HTML templates and centralized logic.

Database Schema Design:

(Note) The default Pocketbase users collection includes a verified boolean field, which will be the source of truth for email verification.

(Update) venues Collection: Add a field move_out_checklist: (JSON, optional, e.g., [{"item": "Take out trash", "checked": false}, ...]).

checklist_submissions Collection:

id, booking: (Relation, Required, Unique), renter: (Relation, Required)

submission_data: (JSON, Required), notes: (Text)

Comprehensive API Design:

Pocketbase API:

POST /api/collections/users/request-verification: Frontend calls this to have Pocketbase resend the verification email.

POST /api/collections/users/confirm-verification: The endpoint visited via the link in the verification email. Pocketbase handles this flow.

Deno Service API:

POST /api/internal/send-invitations: Internal endpoint for the frontend to trigger guest invitations for a specific booking.

Frontend Architecture:

Auth Guarding: The global state will hold the user.verified status. Components for critical actions (e.g., BookingForm.jsx) will be disabled if the user is unverified.

Verification Banner: A persistent, dismissible banner will be displayed at the top of the app for unverified users, reading "Please check your email to verify your account." It will include a "Resend Email" button that triggers the request-verification API call.

ChecklistForm.jsx, ChecklistSubmission.jsx, InvitationForm.jsx components will be added to the dashboard and booking detail pages.

Detailed CRUD Operations:

Verification: This is a state change on the users record, handled by Pocketbase.

Checklists: The template is Updated on the venues record. A checklist_submission is Created by the renter.

Invitations & Alerts: These are event-driven actions, not CRUD operations. They trigger jobs in the Deno service.

Security Considerations: The email invitation endpoint must have strict rate limiting. The "Resend Email" button on the frontend must also be rate-limited to prevent abuse.

Testing Strategy: E2E test for the full registration -> receive welcome email -> receive verification email -> click link -> see "verified" status in-app flow. Test all cron jobs for reminders.

Data Management: Checklists are stored as JSON. Email templates (using MJML for responsiveness) will be stored in the Deno service repository.

Error Handling & Logging: If the Deno service fails to send an email, it should log the error and retry. The user should be informed if an action (like sending invitations) fails.



Feature 6: System Services (Expanded)
Feature Goal: To create a reliable, decoupled backend service that handles all asynchronous tasks, including search syncing, payment processing, and the full suite of lifecycle notifications.

API Relationships: Deno <-> Pocketbase, Deno <-> Meilisearch, Paystack <-> Deno, Deno <-> Email Provider (e.g., Resend).

Detailed Feature Requirements:

Sync Service: Any C/U/D operation on a published venue in Pocketbase must be reflected in the Meilisearch index within seconds.

Webhook Service: Must reliably receive and process payment confirmation webhooks from Paystack. Must be resilient to duplicate events.

Payout Service: Must run on a regular schedule to process pending payouts for completed bookings.

Notification Service: Must handle a wide range of scheduled and transactional emails: Welcome, Booking Alerts, Check-in/Checkout Reminders, Review Prompts, and user-triggered Invitations.

Detailed Implementation Guide:
(The implementation for Sync, Webhook, and Payout services remains the same. The following details the expanded Notification Service logic within the same Deno application.)

System Architecture: Within the existing Deno application, the notificationService.ts becomes a central hub. It will be triggered by other services and multiple cron jobs.

Implementation - Notification Service:

Transactional Triggers:

On New User: A Pocketbase subscription pb.collection('users').subscribe('create', ...) will trigger the service to send a Welcome Email. Pocketbase itself sends the verification email upon registration.

On Booking Status Change: Subscriptions or webhooks on the bookings collection trigger confirmation, denial, and payment success emails.

On Invitation Request: An API call from the frontend triggers the sendInvitationEmail function.

Scheduled Triggers (Cron Jobs):

Upcoming Booking Reminder: Runs hourly. Queries for bookings starting in the next 24-25 hours and sends a reminder.

Check-in Reminder: Runs every 15 mins. Queries for bookings starting in the next 60-75 mins and sends a "Check-in soon" email with key details.

Checkout Reminder: Runs hourly. Queries for bookings ending in the next 2-3 hours and sends a reminder, perhaps including a link to the move-out checklist.

Review Prompt: Runs daily. Queries for bookings that completed the previous day and sends a "Leave a review" prompt.

Database Schema Design:

notifications Collection:

id, booking: (Relation), user: (Relation, Required)

type: (Select: welcome, booking_confirmed, checkin_reminder, review_prompt, etc.)

sent_at: (Date, Required)

This collection is crucial for preventing duplicate notifications from being sent by the cron jobs.

Error Handling & Logging: All external API calls (especially to the email provider) must be wrapped in try...catch. A failed critical email (like a booking confirmation) should trigger an alert for manual review. A robust retry mechanism is essential.


