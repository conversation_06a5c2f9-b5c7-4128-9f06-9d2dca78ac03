import { atom, map, computed } from 'nanostores';
import { pb } from './pocketbase.ts';
import type { User, AuthResult } from '../types/user.ts';

// Type definitions for state
interface SearchFilters {
  minPrice: number | null;
  maxPrice: number | null;
  minCapacity: number | null;
  amenities: string[];
  location: string | null;
  sort: string;
}

interface Notification {
  id: string;
  timestamp: Date;
  type?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  persistent?: boolean;
}

// User authentication state with proper typing
export const userStore = atom<User | null>(null);
export const isAuthenticatedStore = atom<boolean>(false);
export const authLoadingStore = atom<boolean>(true);

// Computed stores for role checking
export const userRolesStore = computed(userStore, (user): string[] => {
  return user?.roles || [];
});

export const isAdminStore = computed(userRolesStore, (roles): boolean => {
  return roles.includes('admin');
});

export const isOwnerStore = computed(userRolesStore, (roles): boolean => {
  return roles.includes('owner');
});

export const isRenterStore = computed(userRolesStore, (roles): boolean => {
  return roles.includes('renter');
});

// Verification state
export const isVerifiedStore = computed(userStore, (user): boolean => {
  return user?.verified || false;
});

export const verificationBannerDismissedStore = atom<boolean>(false);

// UI state
export const themeStore = atom<'light' | 'dark'>('light');
export const sidebarOpenStore = atom<boolean>(false);
export const notificationsStore = atom<Notification[]>([]);

// Search state
export const searchQueryStore = atom<string>('');
export const searchResultsStore = atom<unknown[]>([]);
export const searchLoadingStore = atom<boolean>(false);
export const searchFiltersStore = map<SearchFilters>({
  minPrice: null,
  maxPrice: null,
  minCapacity: null,
  amenities: [],
  location: null,
  sort: 'relevance'
});

// Booking state
export const activeBookingsStore = atom<unknown[]>([]);
export const bookingLoadingStore = atom<boolean>(false);

// Venue state
export const userVenuesStore = atom<unknown[]>([]);
export const venueLoadingStore = atom<boolean>(false);

// Initialize authentication state
export function initializeAuth(): void {
  authLoadingStore.set(true);

  try {
    // First, try to restore auth from HTTP cookie (for client-side hydration)
    if (typeof document !== 'undefined') {
      const authCookie = document.cookie;
      console.log('Auth init - Checking for auth cookie:', authCookie ? 'Present' : 'Not found');

      if (authCookie) {
        // Try to find PocketBase auth cookie with different possible names
        const possibleCookieNames = ['pb_auth', 'pocketbase_auth'];
        let authData = null;

        for (const cookieName of possibleCookieNames) {
          const authMatch = authCookie.match(new RegExp(`${cookieName}=([^;]+)`));
          if (authMatch) {
            try {
              authData = JSON.parse(decodeURIComponent(authMatch[1]));
              console.log('Auth init - Found auth data in cookie:', cookieName);
              break;
            } catch (parseError) {
              console.warn('Auth init - Failed to parse auth cookie:', cookieName, parseError);
            }
          }
        }

        // If we found valid auth data in cookie, restore it to PocketBase
        if (authData && authData.token && authData.model) {
          pb.authStore.save(authData.token, authData.model);
          console.log('Auth init - Restored auth from cookie for user:', authData.model.id);
        }
      }
    }

    // Now check if user is authenticated (either from existing session or restored from cookie)
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model as unknown as User);
      isAuthenticatedStore.set(true);
      console.log('Auth init - User authenticated:', pb.authStore.model.id);

      // Ensure HTTP cookie is set for server-side authentication
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: pb.authStore.token,
          model: pb.authStore.model
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Auth init - Auth cookie set/refreshed');
      }
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);
      console.log('Auth init - No valid authentication found');
    }
  } catch (error) {
    console.error('Failed to initialize auth state:', error);
    userStore.set(null);
    isAuthenticatedStore.set(false);
  } finally {
    authLoadingStore.set(false);
  }
}

// Authentication actions
export const authActions = {
  async login(email: string, password: string): Promise<AuthResult> {
    try {
      authLoadingStore.set(true);
      console.log('Attempting login for:', email);
      
      const authData = await pb.collection('users').authWithPassword(email, password);
      console.log('Login successful for user:', authData.record.id);
      
      userStore.set(authData.record as unknown as User);
      isAuthenticatedStore.set(true);

      // Set HTTP cookie for server-side authentication
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: authData.token,
          model: authData.record
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Login - Auth cookie set');
      }

      return { success: true, user: authData.record as unknown as User };
    } catch (error: unknown) {
      console.error('Login failed:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Login failed';
      if (error instanceof Error && error.message) {
        if (error.message.includes('Failed to authenticate')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.message.includes('verification')) {
          errorMessage = 'Please verify your email address before signing in.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = error.message;
        }
      }
      
      return { success: false, error: errorMessage };
    } finally {
      authLoadingStore.set(false);
    }
  },

  async register(userData: {
    email: string;
    password: string;
    passwordConfirm: string;
    name: string;
  }): Promise<AuthResult> {
    try {
      authLoadingStore.set(true);
      
      console.log('Registering user with data:', { ...userData, password: '[HIDDEN]' });
      
      // Create user account with required fields
      const user = await pb.collection('users').create({
        email: userData.email,
        password: userData.password,
        passwordConfirm: userData.passwordConfirm,
        name: userData.name,
        is_active: true,
        roles: ['renter'] // Default role for new users
      });
      
      console.log('User created successfully:', user.id);
      
      // Send verification email (optional)
      try {
        await pb.collection('users').requestVerification(userData.email);
        console.log('Verification email sent');
      } catch (verificationError) {
        console.warn('Failed to send verification email:', verificationError);
        // Don't fail registration if verification email fails
      }
      
      // Try to auto-login after registration
      try {
        console.log('Attempting auto-login after registration');
        const authData = await pb.collection('users').authWithPassword(
          userData.email,
          userData.password
        );
        
        userStore.set(authData.record as unknown as User);
        isAuthenticatedStore.set(true);

        // Set HTTP cookie for server-side authentication
        if (typeof document !== 'undefined') {
          const authCookie = JSON.stringify({
            token: authData.token,
            model: authData.record
          });
          document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
          console.log('Registration auto-login - Auth cookie set');
        }

        console.log('Auto-login successful');

        return { success: true, user: authData.record as unknown as User, autoLogin: true };
      } catch (loginError) {
        console.warn('Auto-login failed after registration:', loginError);
        // Registration was successful, but auto-login failed
        // User will need to login manually
        return { 
          success: true, 
          user: user as unknown as User,
          autoLogin: false,
          message: 'Account created successfully! Please sign in with your credentials.'
        };
      }
    } catch (error: unknown) {
      console.error('Registration failed:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Registration failed';
      if (error instanceof Error && error.message) {
        if (error.message.includes('email')) {
          errorMessage = 'This email address is already registered. Please use a different email or try signing in.';
        } else if (error.message.includes('password')) {
          errorMessage = 'Password does not meet requirements. Please check and try again.';
        } else {
          errorMessage = error.message;
        }
      }
      
      return { success: false, error: errorMessage };
    } finally {
      authLoadingStore.set(false);
    }
  },

  logout(): void {
    pb.authStore.clear();
    userStore.set(null);
    isAuthenticatedStore.set(false);
    
    // Clear HTTP cookie
    if (typeof document !== 'undefined') {
      document.cookie = 'pb_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      console.log('Logout - Auth cookie cleared');
    }
    
    // Clear other user-specific state
    activeBookingsStore.set([]);
    userVenuesStore.set([]);
    
    // Redirect to home page
    if (typeof globalThis !== 'undefined' && globalThis.window) {
      globalThis.window.location.href = '/';
    }
  },

  async updateProfile(profileData: Partial<User>): Promise<AuthResult> {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const updatedUser = await pb.collection('users').update(user.id, profileData);
      userStore.set(updatedUser as unknown as User);
      return { success: true, user: updatedUser as unknown as User };
    } catch (error: unknown) {
      console.error('Failed to update profile:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to update profile' };
    }
  },

  async addRole(role: string): Promise<AuthResult> {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const currentRoles = user.roles || [];
      if (currentRoles.includes(role)) {
        return { success: true, message: 'Role already exists' };
      }

      const updatedRoles = [...currentRoles, role];
      const updatedUser = await pb.collection('users').update(user.id, {
        roles: updatedRoles
      });

      userStore.set(updatedUser as unknown as User);
      return { success: true, user: updatedUser as unknown as User };
    } catch (error: unknown) {
      console.error('Failed to add role:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to add role' };
    }
  },

  async removeRole(role: string): Promise<AuthResult> {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const currentRoles = user.roles || [];
      const updatedRoles = currentRoles.filter(r => r !== role);

      // Ensure user always has at least 'renter' role
      if (updatedRoles.length === 0) {
        updatedRoles.push('renter');
      }

      const updatedUser = await pb.collection('users').update(user.id, {
        roles: updatedRoles
      });

      userStore.set(updatedUser as unknown as User);
      return { success: true, user: updatedUser as unknown as User };
    } catch (error: unknown) {
      console.error('Failed to remove role:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to remove role' };
    }
  },

  async refreshAuth(): Promise<AuthResult> {
    try {
      if (pb.authStore.isValid) {
        await pb.collection('users').authRefresh();
        userStore.set(pb.authStore.model as unknown as User);
        isAuthenticatedStore.set(true);
        return { success: true };
      }
      return { success: false, error: 'No valid auth session' };
    } catch (error: unknown) {
      console.error('Auth refresh failed:', error);
      this.logout();
      return { success: false, error: error instanceof Error ? error.message : 'Auth refresh failed' };
    }
  },

  // Check if current user has specific role
  hasRole(role: string): boolean {
    const user = userStore.get();
    return user?.roles?.includes(role) || false;
  },

  // Check if current user is admin
  isAdmin(): boolean {
    return this.hasRole('admin');
  },

  // Check if current user is owner
  isOwner(): boolean {
    return this.hasRole('owner');
  },

  // Verification actions
  async requestVerification(): Promise<{ success: boolean; error?: string }> {
    try {
      const { requestEmailVerification } = await import('./pocketbase.ts');
      const result = await requestEmailVerification();

      if (result.success) {
        notificationActions.add({
          type: 'success',
          message: 'Verification email sent! Please check your inbox.'
        });
      }

      return result;
    } catch (error) {
      console.error('Failed to request verification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to request verification'
      };
    }
  },

  async confirmVerification(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { confirmEmailVerification } = await import('./pocketbase.ts');
      const result = await confirmEmailVerification(token);

      if (result.success) {
        // Refresh user data to get updated verification status
        await this.refreshAuth();
        notificationActions.add({
          type: 'success',
          message: 'Email verified successfully!'
        });
      }

      return result;
    } catch (error) {
      console.error('Failed to confirm verification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to confirm verification'
      };
    }
  },

  dismissVerificationBanner(): void {
    verificationBannerDismissedStore.set(true);
    // Store dismissal in localStorage to persist across sessions
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('verificationBannerDismissed', 'true');
    }
  },

  resetVerificationBannerDismissal(): void {
    verificationBannerDismissedStore.set(false);
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('verificationBannerDismissed');
    }
  }
};

// Search actions
export const searchActions = {
  setQuery(query: string): void {
    searchQueryStore.set(query);
  },

  setResults(results: unknown[]): void {
    searchResultsStore.set(results);
  },

  setLoading(loading: boolean): void {
    searchLoadingStore.set(loading);
  },

  updateFilters(filters: Partial<SearchFilters>): void {
    const currentFilters = searchFiltersStore.get();
    searchFiltersStore.set({ ...currentFilters, ...filters });
  },

  clearFilters(): void {
    searchFiltersStore.set({
      minPrice: null,
      maxPrice: null,
      minCapacity: null,
      amenities: [],
      location: null,
      sort: 'relevance'
    });
  }
};

// Notification actions
export const notificationActions = {
  add(notification: Omit<Notification, 'id' | 'timestamp'>): void {
    const notifications = notificationsStore.get();
    const newNotification: Notification = {
      id: Date.now().toString(),
      timestamp: new Date(),
      ...notification
    };
    notificationsStore.set([...notifications, newNotification]);

    // Auto-remove after 5 seconds for non-persistent notifications
    if (!notification.persistent) {
      setTimeout(() => {
        this.remove(newNotification.id);
      }, 5000);
    }
  },

  remove(id: string): void {
    const notifications = notificationsStore.get();
    notificationsStore.set(notifications.filter(n => n.id !== id));
  },

  clear(): void {
    notificationsStore.set([]);
  }
};

// Theme actions
export const themeActions = {
  setTheme(theme: 'light' | 'dark'): void {
    themeStore.set(theme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', theme);
      document.documentElement.setAttribute('data-theme', theme);
    }
  },

  toggleTheme(): void {
    const currentTheme = themeStore.get();
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  },

  initializeTheme(): void {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' || 'light';
      this.setTheme(savedTheme);
    }
  }
};

// UI actions
export const uiActions = {
  toggleSidebar(): void {
    sidebarOpenStore.set(!sidebarOpenStore.get());
  },

  setSidebarOpen(open: boolean): void {
    sidebarOpenStore.set(open);
  }
};

// Data loading actions
export const dataActions = {
  async loadUserBookings(): Promise<{ success: boolean; bookings?: unknown[]; error?: string }> {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      bookingLoadingStore.set(true);
      const bookings = await pb.collection('bookings').getList(1, 50, {
        filter: `renter.id = "${user.id}" || venue.owner.id = "${user.id}"`,
        expand: 'venue,renter,venue.owner',
        sort: '-created'
      });

      activeBookingsStore.set(bookings.items);
      return { success: true, bookings: bookings.items };
    } catch (error: unknown) {
      console.error('Failed to load bookings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to load bookings' };
    } finally {
      bookingLoadingStore.set(false);
    }
  },

  async loadUserVenues(): Promise<{ success: boolean; venues?: unknown[]; error?: string }> {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      venueLoadingStore.set(true);
      const venues = await pb.collection('venues').getList(1, 50, {
        filter: `owner.id = "${user.id}"`,
        sort: '-created'
      });

      userVenuesStore.set(venues.items);
      return { success: true, venues: venues.items };
    } catch (error: unknown) {
      console.error('Failed to load venues:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to load venues' };
    } finally {
      venueLoadingStore.set(false);
    }
  }
};

// Initialize stores on app load
if (typeof window !== 'undefined') {
  // Initialize auth state
  initializeAuth();

  // Initialize theme
  themeActions.initializeTheme();

  // Initialize verification banner dismissal state
  const bannerDismissed = localStorage.getItem('verificationBannerDismissed') === 'true';
  verificationBannerDismissedStore.set(bannerDismissed);

  // Listen for auth changes
  pb.authStore.onChange(() => {
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model as unknown as User);
      isAuthenticatedStore.set(true);
      
      // Sync HTTP cookie when auth changes
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: pb.authStore.token,
          model: pb.authStore.model
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Auth change - Auth cookie synced');
      }
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);
      
      // Clear HTTP cookie when auth is cleared
      if (typeof document !== 'undefined') {
        document.cookie = 'pb_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        console.log('Auth change - Auth cookie cleared');
      }
    }
  });
}

// Export all stores and actions
export default {
  // Stores
  userStore,
  isAuthenticatedStore,
  authLoadingStore,
  userRolesStore,
  isAdminStore,
  isOwnerStore,
  isRenterStore,
  isVerifiedStore,
  verificationBannerDismissedStore,
  themeStore,
  sidebarOpenStore,
  notificationsStore,
  searchQueryStore,
  searchResultsStore,
  searchLoadingStore,
  searchFiltersStore,
  activeBookingsStore,
  bookingLoadingStore,
  userVenuesStore,
  venueLoadingStore,

  // Actions
  authActions,
  searchActions,
  notificationActions,
  themeActions,
  uiActions,
  dataActions
};
