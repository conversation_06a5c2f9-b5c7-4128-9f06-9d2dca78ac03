// Booking-related type definitions for Trodoo

export interface Booking {
  id: string;
  renter: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  owner: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  venue: {
    id: string;
    title: string;
    address: string;
    owner: {
      id: string;
      name: string;
      email: string;
    };
    average_rating?: number;
    review_count?: number;
    move_out_checklist?: {
      id: string;
      text: string;
    }[];
  };
  start_date: string;
  end_date: string;
  total_price: number;
  platform_fee: number;
  payout_amount: number;
  status: BookingStatus;
  paystack_ref?: string;
  special_requests?: string;
  created: string;
  updated: string;
}

export type BookingStatus =
  | 'pending'     // Waiting for owner approval
  | 'confirmed'   // Approved by owner, waiting for payment
  | 'paid'        // Payment completed
  | 'completed'   // Event finished
  | 'cancelled'   // Cancelled by either party
  | 'denied';     // Denied by owner

export interface BookingRequest {
  venue: string;
  owner: string;
  start_date: string;
  end_date: string;
  total_price: number;
  platform_fee: number;
  payout_amount: number;
  special_requests?: string;
}

export interface BookingFormData {
  venue_id: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  guest_count: number;
  total_hours: number;
  total_amount: number;
  special_requests?: string;
}

// Chat/Messaging types
export interface ChatMessage {
  id: string;
  booking_id: string;
  sender_id: string;
  sender_name: string;
  message: string;
  timestamp: string;
  is_read: boolean;
}

export interface ChatThread {
  booking_id: string;
  messages: ChatMessage[];
  participants: {
    renter: {
      id: string;
      name: string;
      avatar?: string;
    };
    owner: {
      id: string;
      name: string;
      avatar?: string;
    };
  };
}

// Checklist types
export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  required: boolean;
}

export interface ChecklistTemplate {
  id: string;
  venue_id: string;
  title: string;
  items: ChecklistItem[];
  created: string;
  updated: string;
}

export interface ChecklistSubmission {
  id: string;
  booking_id: string;
  checklist_data: ChecklistItem[];
  notes?: string;
  submitted_at: string;
  submitted_by: string;
}

// Invitation types
export interface EventInvitation {
  id: string;
  booking_id: string;
  event_title: string;
  event_description?: string;
  guest_emails: string[];
  custom_message?: string;
  sent_at: string;
  sent_by: string;
}

export interface InvitationFormData {
  event_title: string;
  event_description?: string;
  guest_emails: string[];
  custom_message?: string;
}

// Payment types
export interface PaymentDetails {
  amount: number;
  currency: string;
  reference: string;
  status: 'pending' | 'success' | 'failed';
  gateway: 'paystack';
  metadata?: Record<string, string | number | boolean>;
}

export interface Payout {
  id: string;
  booking_id: string;
  owner_id: string;
  amount: number;
  platform_fee: number;
  net_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paystack_transfer_code?: string;
  processed_at?: string;
  created: string;
}

// Booking statistics
export interface BookingStats {
  total_bookings: number;
  active_bookings: number;
  completed_bookings: number;
  total_revenue: number;
  average_booking_value: number;
  booking_trends: {
    period: string;
    count: number;
    revenue: number;
  }[];
}

// API response types
export interface BookingListResponse {
  items: Booking[];
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
}

export interface BookingActionResponse {
  success: boolean;
  message?: string;
  booking?: Booking;
  error?: string;
}

// Form validation types
export interface BookingValidationErrors {
  start_date?: string;
  end_date?: string;
  guest_count?: string;
  special_requests?: string;
  general?: string;
}

export interface ChecklistValidationErrors {
  title?: string;
  items?: string;
  general?: string;
}

export interface InvitationValidationErrors {
  event_title?: string;
  guest_emails?: string;
  general?: string;
}
