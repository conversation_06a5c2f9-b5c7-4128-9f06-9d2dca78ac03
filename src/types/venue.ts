// Venue-related type definitions for Trodoo

export interface VenueAddress {
  street: string;
  city: string;
  state?: string;
  country: string;
  postal_code?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface ChecklistItem {
  id: string;
  text: string;
}

export interface Venue {
  id: string;
  title: string;
  description: string;
  address: VenueAddress;
  capacity: number;
  price_per_hour: number;
  amenities: string[];
  standard_photos: string[];
  pano_photo?: string;
  rental_agreement_pdf?: string;
  is_published: boolean;
  move_out_checklist?: ChecklistItem[];
  owner: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  average_rating?: number;
  review_count?: number;
  total_bookings?: number;
  created: string;
  updated: string;
}

export interface VenueFormData {
  title: string;
  description: string;
  address: VenueAddress;
  capacity: number;
  price_per_hour: number;
  amenities: string[];
  standard_photos: File[];
  pano_photo?: File;
  rental_agreement_pdf?: File;
  is_published: boolean;
  move_out_checklist?: ChecklistItem[];
}

export interface VenueSearchFilters {
  query?: string;
  minPrice?: number;
  maxPrice?: number;
  minCapacity?: number;
  amenities?: string[];
  location?: {
    lat: number;
    lng: number;
    radius: number; // in kilometers
  };
  sort?: 'relevance' | 'price_asc' | 'price_desc' | 'capacity_asc' | 'capacity_desc' | 'rating' | 'newest';
  page?: number;
  limit?: number;
}

export interface VenueSearchResult {
  venues: Venue[];
  totalCount: number;
  facets: {
    amenities: Record<string, number>;
    priceRanges: Record<string, number>;
    capacityRanges: Record<string, number>;
  };
  processingTimeMs: number;
}

// Review types
export interface Review {
  id: string;
  booking_id: string;
  venue_id: string;
  renter: {
    id: string;
    name: string;
    avatar?: string;
  };
  rating: number;
  comment: string;
  owner_response?: string;
  created: string;
  updated: string;
}

export interface ReviewFormData {
  rating: number;
  comment: string;
}

export interface ReviewStats {
  average_rating: number;
  total_reviews: number;
  rating_distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// Amenity types
export interface Amenity {
  id: string;
  name: string;
  icon?: string;
  category: AmenityCategory;
  description?: string;
}

export type AmenityCategory = 
  | 'basic'        // WiFi, Parking, etc.
  | 'audio_visual' // Sound system, Projector, etc.
  | 'catering'     // Kitchen, Bar, etc.
  | 'comfort'      // AC, Heating, etc.
  | 'accessibility' // Wheelchair access, etc.
  | 'outdoor'      // Garden, Terrace, etc.
  | 'security'     // CCTV, Security guard, etc.
  | 'other';

// Venue availability
export interface VenueAvailability {
  venue_id: string;
  date: string;
  available_slots: TimeSlot[];
  blocked_slots: TimeSlot[];
  pricing_overrides?: {
    slot: TimeSlot;
    price_per_hour: number;
  }[];
}

export interface TimeSlot {
  start_time: string; // HH:MM format
  end_time: string;   // HH:MM format
}

// Venue statistics for owners
export interface VenueStats {
  venue_id: string;
  total_bookings: number;
  total_revenue: number;
  average_rating: number;
  occupancy_rate: number; // percentage
  booking_trends: {
    period: string;
    bookings: number;
    revenue: number;
  }[];
  popular_amenities: string[];
  peak_hours: {
    hour: number;
    booking_count: number;
  }[];
}

// Venue management
export interface VenueSettings {
  venue_id: string;
  auto_approve_bookings: boolean;
  advance_booking_days: number;
  cancellation_policy: CancellationPolicy;
  house_rules: string[];
  check_in_instructions: string;
  check_out_instructions: string;
  emergency_contact: {
    name: string;
    phone: string;
  };
}

export type CancellationPolicy = 
  | 'flexible'   // Full refund 24 hours before
  | 'moderate'   // Full refund 5 days before, 50% within 5 days
  | 'strict'     // Full refund 14 days before, 50% within 14 days
  | 'super_strict'; // No refund within 60 days

// API response types
export interface VenueListResponse {
  items: Venue[];
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
}

export interface VenueActionResponse {
  success: boolean;
  message?: string;
  venue?: Venue;
  error?: string;
}

// Form validation types
export interface VenueValidationErrors {
  title?: string;
  description?: string;
  address?: string;
  capacity?: string;
  price_per_hour?: string;
  amenities?: string;
  standard_photos?: string;
  pano_photo?: string;
  rental_agreement_pdf?: string;
  general?: string;
}

export interface ReviewValidationErrors {
  rating?: string;
  comment?: string;
  general?: string;
}

// Search suggestion types
export interface VenueSearchSuggestion {
  id: string;
  title: string;
  address: string;
  type: 'venue' | 'location' | 'amenity';
  highlight?: string;
}

// Venue card display options
export interface VenueCardOptions {
  showActions?: boolean;
  showOwner?: boolean;
  showStats?: boolean;
  compact?: boolean;
  className?: string;
}
