import { useStore } from '@nanostores/react';
import { useEffect, useState } from 'react';
import VerificationBanner from '../auth/VerificationBanner.tsx';
import state from '../../lib/state.ts';

interface AppWrapperProps {
  children: React.ReactNode;
}

export default function AppWrapper({ children }: AppWrapperProps) {
  const user = useStore(state.userStore);
  const isAuthenticated = useStore(state.isAuthenticatedStore);
  const isVerified = useStore(state.isVerifiedStore);
  const bannerDismissed = useStore(state.verificationBannerDismissedStore);
  
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Show banner if user is authenticated but not verified and hasn't dismissed it
    setShowBanner(isAuthenticated && !isVerified && !bannerDismissed);
  }, [isAuthenticated, isVerified, bannerDismissed]);

  const handleDismissBanner = () => {
    state.authActions.dismissVerificationBanner();
  };

  const handleResendEmail = async (): Promise<void> => {
    await state.authActions.requestVerification();
  };

  return (
    <>
      <VerificationBanner
        isVisible={showBanner}
        onDismiss={handleDismissBanner}
        onResendEmail={handleResendEmail}
        userEmail={user?.email}
      />
      <div className={showBanner ? 'pt-16' : ''}>
        {children}
      </div>
    </>
  );
}
