import { useState, useEffect } from 'react';
import {
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  CreditCard,
  MessageSquare,
  UserPlus
} from 'lucide-react';
import type { Booking } from '../../types/booking.ts';
import type { ChecklistItem } from '../../types/venue.ts';
import Button from '../common/Button.tsx';
import MessagingWindow from '../transactions/MessagingWindow.tsx';
import PaystackPayment from '../transactions/PaystackPayment.tsx';
import ChecklistForm from '../venues/ChecklistForm.tsx';
import ChecklistSubmission from '../venues/ChecklistSubmission.tsx';
import InvitationForm from '../venues/InvitationForm.tsx';
import {
  getBooking,
  approveBooking,
  denyBooking,
  cancelBooking,
  createChecklistSubmission,
  getChecklistSubmission,
  sendGuestInvitations
} from '../../lib/pocketbase.ts';
import { formatAmount } from '../../lib/paystack.ts';

interface BookingDetailsProps {
  bookingId: string;
  currentUserId: string;
  onBookingUpdate?: (booking: Booking) => void;
  className?: string;
}

interface ChecklistSubmissionData {
  id: string;
  booking: string;
  renter: {
    id: string;
    name: string;
    email: string;
  };
  submission_data: Record<string, boolean>;
  notes?: string;
  created: string;
  updated: string;
}

interface BookingDetailsState {
  booking: Booking | null;
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  showPayment: boolean;
  showMessaging: boolean;
  checklistSubmission: ChecklistSubmissionData | null;
  showInvitationForm: boolean;
  isSubmittingChecklist: boolean;
  isSendingInvitations: boolean;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  confirmed: 'bg-blue-100 text-blue-800 border-blue-200',
  paid: 'bg-green-100 text-green-800 border-green-200',
  completed: 'bg-gray-100 text-gray-800 border-gray-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  denied: 'bg-red-100 text-red-800 border-red-200',
};

const statusLabels = {
  pending: 'Pending Approval',
  confirmed: 'Confirmed - Payment Required',
  paid: 'Paid',
  completed: 'Completed',
  cancelled: 'Cancelled',
  denied: 'Denied',
};

export default function BookingDetails({
  bookingId,
  currentUserId,
  onBookingUpdate,
  className = ''
}: BookingDetailsProps) {
  const [state, setState] = useState<BookingDetailsState>({
    booking: null,
    isLoading: true,
    isUpdating: false,
    error: null,
    showPayment: false,
    showMessaging: false,
    checklistSubmission: null,
    showInvitationForm: false,
    isSubmittingChecklist: false,
    isSendingInvitations: false
  });

  // Load booking details
  const loadBooking = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await getBooking(bookingId);
      if (result.success && result.booking) {
        setState(prev => ({
          ...prev,
          booking: result.booking || null,
          isLoading: false
        }));

        if (onBookingUpdate) {
          onBookingUpdate(result.booking);
        }
      } else {
        setState(prev => ({ 
          ...prev, 
          error: result.error || 'Failed to load booking', 
          isLoading: false 
        }));
      }
    } catch (_error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to load booking',
        isLoading: false
      }));
    }
  };

  useEffect(() => {
    loadBooking();
  }, [bookingId]);

  // Load checklist submission if it exists
  const loadChecklistSubmission = async () => {
    if (!state.booking) return;

    try {
      const result = await getChecklistSubmission(state.booking.id);
      if (result.success && result.submission) {
        setState(prev => ({ ...prev, checklistSubmission: result.submission as unknown as ChecklistSubmissionData }));
      }
    } catch (error) {
      console.error('Failed to load checklist submission:', error);
    }
  };

  // Load checklist submission when booking is loaded
  useEffect(() => {
    if (state.booking && isRenter && ['paid', 'completed'].includes(state.booking.status)) {
      loadChecklistSubmission();
    }
  }, [state.booking]);

  // Check if booking is in final day (within 24 hours of end date)
  const isInFinalDay = (booking: Booking): boolean => {
    const now = new Date();
    const endDate = new Date(booking.end_date);
    const timeDiff = endDate.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 3600);
    return hoursDiff <= 24 && hoursDiff > 0;
  };

  // Check if checklist should be shown
  const shouldShowChecklist = (): boolean => {
    if (!state.booking || !isRenter) return false;
    if (!state.booking.venue.move_out_checklist || state.booking.venue.move_out_checklist.length === 0) return false;
    if (state.checklistSubmission) return false; // Already submitted
    return isInFinalDay(state.booking) && state.booking.status === 'paid';
  };

  // Check if invitation button should be shown
  const shouldShowInvitationButton = (): boolean => {
    if (!state.booking || !isRenter) return false;
    return state.booking.status === 'paid';
  };

  // Handle checklist submission
  const handleChecklistSubmission = async (submissionData: Record<string, boolean>, notes?: string) => {
    if (!state.booking) return;

    setState(prev => ({ ...prev, isSubmittingChecklist: true, error: null }));

    try {
      const result = await createChecklistSubmission({
        booking: state.booking.id,
        submission_data: submissionData,
        notes
      });

      if (result.success) {
        await loadChecklistSubmission(); // Reload to show submitted checklist
      } else {
        setState(prev => ({
          ...prev,
          error: result.error || 'Failed to submit checklist',
          isSubmittingChecklist: false
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to submit checklist',
        isSubmittingChecklist: false
      }));
    } finally {
      setState(prev => ({ ...prev, isSubmittingChecklist: false }));
    }
  };

  // Handle invitation sending
  const handleSendInvitations = async (data: { message: string; emails: string[] }) => {
    if (!state.booking) return;

    setState(prev => ({ ...prev, isSendingInvitations: true, error: null }));

    try {
      const result = await sendGuestInvitations({
        bookingId: state.booking.id,
        message: data.message,
        emails: data.emails
      });

      if (result.success) {
        setState(prev => ({ ...prev, showInvitationForm: false }));
      } else {
        setState(prev => ({
          ...prev,
          error: result.error || 'Failed to send invitations',
          isSendingInvitations: false
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to send invitations',
        isSendingInvitations: false
      }));
    } finally {
      setState(prev => ({ ...prev, isSendingInvitations: false }));
    }
  };

  // Handle booking actions
  const handleBookingAction = async (action: 'approve' | 'deny' | 'cancel') => {
    if (!state.booking) return;

    setState(prev => ({ ...prev, isUpdating: true, error: null }));

    try {
      let result;
      switch (action) {
        case 'approve':
          result = await approveBooking(state.booking.id);
          break;
        case 'deny':
          result = await denyBooking(state.booking.id);
          break;
        case 'cancel':
          result = await cancelBooking(state.booking.id);
          break;
      }

      if (result.success) {
        await loadBooking(); // Reload to get updated status
      } else {
        setState(prev => ({ 
          ...prev, 
          error: result.error || `Failed to ${action} booking`,
          isUpdating: false
        }));
      }
    } catch (_error) {
      setState(prev => ({
        ...prev,
        error: `Failed to ${action} booking`,
        isUpdating: false
      }));
    } finally {
      setState(prev => ({ ...prev, isUpdating: false }));
    }
  };

  // Handle payment success
  const handlePaymentSuccess = (_reference: string) => {
    setState(prev => ({ ...prev, showPayment: false }));
    loadBooking(); // Reload to get updated status
  };

  // Handle payment error
  const handlePaymentError = (error: string) => {
    setState(prev => ({ ...prev, error }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Check if current user is the owner
  const isOwner = state.booking?.owner.id === currentUserId;
  const isRenter = state.booking?.renter.id === currentUserId;

  if (state.isLoading) {
    return (
      <div className={`bg-white rounded-xl shadow-card border border-slate-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-slate-200 rounded w-1/3"></div>
          <div className="h-4 bg-slate-200 rounded w-1/2"></div>
          <div className="h-32 bg-slate-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (state.error || !state.booking) {
    return (
      <div className={`bg-white rounded-xl shadow-card border border-slate-200 p-6 ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-slate-900 mb-2">
            {state.error || 'Booking not found'}
          </h3>
          <Button onClick={loadBooking} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const { booking } = state;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Booking Header */}
      <div className="bg-white rounded-xl shadow-card border border-slate-200 p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-slate-900 mb-2">
              {booking.venue.title}
            </h1>
            <div className="flex items-center text-slate-600 mb-2">
              <MapPin className="w-4 h-4 mr-1" />
              <span className="text-sm">{booking.venue.address}</span>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full border text-sm font-medium ${
            statusColors[booking.status]
          }`}>
            {statusLabels[booking.status]}
          </div>
        </div>

        {/* Booking Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center">
              <Calendar className="w-5 h-5 text-slate-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-900">Check-in</p>
                <p className="text-sm text-slate-600">{formatDate(booking.start_date)}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-slate-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-900">Check-out</p>
                <p className="text-sm text-slate-600">{formatDate(booking.end_date)}</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <Users className="w-5 h-5 text-slate-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-900">
                  {isOwner ? 'Renter' : 'Venue Owner'}
                </p>
                <p className="text-sm text-slate-600">
                  {isOwner ? booking.renter.name : booking.owner.name}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <DollarSign className="w-5 h-5 text-slate-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-900">Total Amount</p>
                <p className="text-lg font-bold text-primary-green">
                  {formatAmount(booking.total_price)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Special Requests */}
        {booking.special_requests && (
          <div className="mt-6 pt-6 border-t border-slate-200">
            <h3 className="text-sm font-medium text-slate-900 mb-2">Special Requests</h3>
            <p className="text-sm text-slate-600">{booking.special_requests}</p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-card border border-slate-200 p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Actions</h3>
        
        <div className="flex flex-wrap gap-3">
          {/* Owner Actions */}
          {isOwner && booking.status === 'pending' && (
            <>
              <Button
                onClick={() => handleBookingAction('approve')}
                isLoading={state.isUpdating}
                disabled={state.isUpdating}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Approve Booking
              </Button>
              <Button
                onClick={() => handleBookingAction('deny')}
                isLoading={state.isUpdating}
                disabled={state.isUpdating}
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Deny Booking
              </Button>
            </>
          )}

          {/* Renter Actions */}
          {isRenter && booking.status === 'confirmed' && (
            <Button
              onClick={() => setState(prev => ({ ...prev, showPayment: true }))}
              className="bg-yellow-600 hover:bg-yellow-700"
            >
              <CreditCard className="w-4 h-4 mr-2" />
              Pay Now
            </Button>
          )}

          {/* Cancel Action */}
          {(isRenter || isOwner) && ['pending', 'confirmed'].includes(booking.status) && (
            <Button
              onClick={() => handleBookingAction('cancel')}
              isLoading={state.isUpdating}
              disabled={state.isUpdating}
              variant="outline"
              className="border-red-300 text-red-600 hover:bg-red-50"
            >
              Cancel Booking
            </Button>
          )}

          {/* Messaging Action */}
          {['confirmed', 'paid', 'completed'].includes(booking.status) && (
            <Button
              onClick={() => setState(prev => ({ ...prev, showMessaging: !prev.showMessaging }))}
              variant="outline"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              {state.showMessaging ? 'Hide Messages' : 'Show Messages'}
            </Button>
          )}

          {/* Invitation Action */}
          {shouldShowInvitationButton() && (
            <Button
              onClick={() => setState(prev => ({ ...prev, showInvitationForm: true }))}
              variant="outline"
              className="border-primary-green text-primary-green hover:bg-primary-greenSubtle"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Invite Guests
            </Button>
          )}
        </div>
      </div>

      {/* Payment Component */}
      {state.showPayment && isRenter && booking.status === 'confirmed' && (
        <PaystackPayment
          booking={booking}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          onClose={() => setState(prev => ({ ...prev, showPayment: false }))}
        />
      )}

      {/* Messaging Component */}
      {state.showMessaging && ['confirmed', 'paid', 'completed'].includes(booking.status) && (
        <MessagingWindow
          booking={booking}
          currentUserId={currentUserId}
        />
      )}

      {/* Move-out Checklist */}
      {shouldShowChecklist() && booking.venue.move_out_checklist && (
        <ChecklistForm
          items={booking.venue.move_out_checklist}
          onSubmit={handleChecklistSubmission}
          isLoading={state.isSubmittingChecklist}
        />
      )}

      {/* Checklist Submission Display */}
      {state.checklistSubmission && booking.venue.move_out_checklist && (
        <ChecklistSubmission
          submission={state.checklistSubmission}
          checklistItems={booking.venue.move_out_checklist}
        />
      )}

      {/* Invitation Form */}
      {state.showInvitationForm && (
        <InvitationForm
          isOpen={state.showInvitationForm}
          onClose={() => setState(prev => ({ ...prev, showInvitationForm: false }))}
          onSubmit={handleSendInvitations}
          bookingDetails={{
            venueName: booking.venue.title,
            startDate: booking.start_date,
            endDate: booking.end_date,
            renterName: booking.renter.name
          }}
          isLoading={state.isSendingInvitations}
        />
      )}

      {/* Error Display */}
      {state.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-red-600">{state.error}</p>
          </div>
        </div>
      )}
    </div>
  );
}
